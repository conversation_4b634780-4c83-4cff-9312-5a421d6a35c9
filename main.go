package main

import (
	"encoding/json"
	"go/ast"
	"go/parser"
	"go/token"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strings"
)

type FunctionInfo struct {
	Name  string   `json:"name"`
	File  string   `json:"file"`
	Calls []string `json:"calls"`
	Code  string   `json:"code"`
}

type EndpointChain struct {
	Handler   FunctionInfo   `json:"handler"`
	CallChain []FunctionInfo `json:"chain"`
}

var symbolTable = map[string]FunctionInfo{}
var fset = token.NewFileSet()

func main() {
	roots := []string{
		"./app/api/routes",
		"./pkg/domains",
	}

	for _, root := range roots {
		err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}
			if !info.IsDir() && strings.HasSuffix(path, ".go") {
				parseFile(path)
			}
			return nil
		})
		if err != nil {
			log.Fatal(err)
		}
	}

	var endpoints []EndpointChain
	for _, fn := range symbolTable {
		if !isHandler(fn) {
			continue
		}
		chain := []FunctionInfo{}
		for _, call := range fn.Calls {
			if !strings.HasPrefix(call, "s.") {
				continue
			}
			serviceName := strings.TrimPrefix(call, "s.")
			for _, sfn := range symbolTable {
				if sfn.Name != serviceName || isHandler(sfn) {
					continue
				}
				chain = append(chain, sfn)
				for _, serviceCall := range sfn.Calls {
					if !strings.HasPrefix(serviceCall, "s.repository.") {
						continue
					}
					repoName := strings.TrimPrefix(serviceCall, "s.repository.")
					for _, rfn := range symbolTable {
						if rfn.Name == repoName && !isHandler(rfn) {
							chain = append(chain, rfn)
						}
					}
				}
			}
		}

		visited := map[string]bool{}
		buildChain(fn, &chain, visited)

		endpoints = append(endpoints, EndpointChain{
			Handler:   fn,
			CallChain: chain,
		})
	}

	for _, ep := range endpoints {
		r, _ := json.Marshal(ep)
		sendToAgent(&r)
	}

	// out, _ := json.MarshalIndent(endpoints, "", "  ")
	// fmt.Println(string(out))
}

func parseFile(path string) {
	node, err := parser.ParseFile(fset, path, nil, parser.ParseComments) // ast'ye çevirilir
	if err != nil {
		panic(err)
	}

	ast.Inspect(node, func(n ast.Node) bool {
		// sadece fonksiyonları alır, gövdesi olmayanlar hariç
		fn, ok := n.(*ast.FuncDecl)
		if !ok || fn.Body == nil {
			return true
		}

		calls := []string{}
		ast.Inspect(fn.Body, func(n ast.Node) bool {
			if call, ok := n.(*ast.CallExpr); ok {
				if sel, ok := call.Fun.(*ast.SelectorExpr); ok {
					switch x := sel.X.(type) {
					case *ast.Ident:
						if x.Name != "c" && x.Name != "ctx" &&
							x.Name != "state" && x.Name != "localizer" &&
							x.Name != "monolog" && x.Name != "sayelog" &&
							x.Name != "err" {
							calls = append(calls, x.Name+"."+sel.Sel.Name)
						}
					case *ast.SelectorExpr:
						if xid, ok := x.X.(*ast.Ident); ok {
							if xid.Name != "c" && xid.Name != "ctx" &&
								xid.Name != "state" && xid.Name != "localizer" &&
								xid.Name != "monolog" && xid.Name != "sayelog" &&
								xid.Name != "err" {
								calls = append(calls, xid.Name+"."+x.Sel.Name+"."+sel.Sel.Name)
							}
						}
					}
				}
			}
			return true
		})

		key := fn.Name.Name
		if fn.Recv != nil && len(fn.Recv.List) > 0 {
			if star, ok := fn.Recv.List[0].Type.(*ast.StarExpr); ok {
				if ident, ok := star.X.(*ast.Ident); ok {
					key = ident.Name + "." + fn.Name.Name
				}
			}
		}

		src := extractSource(path, fn.Pos(), fn.End())
		symbolTable[key] = FunctionInfo{
			Name:  fn.Name.Name,
			File:  path,
			Calls: calls,
			Code:  src,
		}
		return true
	})
}

func extractSource(path string, start, end token.Pos) string {
	data, _ := ioutil.ReadFile(path)
	file := fset.File(start)
	return string(data[file.Offset(start):file.Offset(end)])
}

func isHandler(fn FunctionInfo) bool {
	return strings.Contains(fn.Code, "gin.Context") || strings.Contains(fn.Code, "echo.Context")
}

func buildChain(fn FunctionInfo, chain *[]FunctionInfo, visited map[string]bool) {
	for _, call := range fn.Calls {
		if visited[call] {
			continue
		}
		if next, ok := symbolTable[call]; ok {
			visited[call] = true
			*chain = append(*chain, next)
			buildChain(next, chain, visited)
		}
	}
}

func sendToAgent(out *[]byte) {

}
